{"name": "wcwidth", "version": "1.0.1", "description": "Port of C's wcwidth() and wcswidth()", "author": "<PERSON>", "contributors": ["Woong <PERSON> <<EMAIL>> (http://code.woong.org/)"], "main": "index.js", "dependencies": {"defaults": "^1.0.3"}, "devDependencies": {"tape": "^4.5.1"}, "license": "MIT", "keywords": ["wide character", "wc", "wide character string", "wcs", "terminal", "width", "wcwidth", "wcswidth"], "directories": {"doc": "docs", "test": "test"}, "scripts": {"test": "tape test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/timoxley/wcwidth.git"}, "bugs": {"url": "https://github.com/timoxley/wcwidth/issues"}, "homepage": "https://github.com/timoxley/wcwidth#readme"}