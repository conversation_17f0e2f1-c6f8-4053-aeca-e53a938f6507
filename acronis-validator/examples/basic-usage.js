#!/usr/bin/env node

/**
 * Basic usage examples for the Acronis Validator library
 */

const { AcronisValidatorLib } = require('../src/index');

async function basicValidationExample() {
    console.log('🔍 Basic Validation Example');
    console.log('==========================\n');

    const validator = new AcronisValidatorLib({
        timeout: 30000,
        retryAttempts: 2
    });

    try {
        // Example validation (will fail with fake credentials)
        const result = await validator.validate('<EMAIL>', 'fake-password');
        
        console.log('Validation Result:');
        console.log(`Success: ${result.success}`);
        console.log(`Exit Code: ${result.exitCode}`);
        
        if (!result.success) {
            console.log('Error Details:');
            console.log(result.formatted);
        }

    } catch (error) {
        console.error('Validation Error:', error.message);
    }
}

async function proxyConfigExample() {
    console.log('\n🌐 Proxy Configuration Example');
    console.log('===============================\n');

    const validator = new AcronisValidatorLib();

    // Test proxy configuration
    try {
        const proxyInfo = validator.getProxyInfo();
        console.log('Current Proxy Info:', proxyInfo);

        // Configure from environment
        const envProxy = validator.configureFromEnvironment();
        if (envProxy) {
            console.log('Proxy configured from environment:', envProxy);
        } else {
            console.log('No proxy configured in environment');
        }

    } catch (error) {
        console.error('Proxy Configuration Error:', error.message);
    }
}

async function proxyTestExample() {
    console.log('\n🧪 Proxy Test Example');
    console.log('======================\n');

    const validator = new AcronisValidatorLib();

    try {
        // Test a proxy (this will fail with fake proxy)
        const testResult = await validator.testProxy('http://fake-proxy.example.com:8080');
        console.log('Proxy Test Result:', testResult);

    } catch (error) {
        console.log('Proxy test failed (expected with fake proxy):', error.message);
    }
}

async function main() {
    console.log('🚀 Acronis Validator - Usage Examples\n');

    await basicValidationExample();
    await proxyConfigExample();
    await proxyTestExample();

    console.log('\n✅ Examples completed!');
    console.log('\nFor real validation, use:');
    console.log('node src/cli.js -e <EMAIL> -p your-password');
    console.log('\nFor interactive mode:');
    console.log('node src/cli.js --interactive');
    console.log('\nFor proxy usage:');
    console.log('node src/cli.js -e <EMAIL> -p password --proxy http://proxy.company.com:8080');
}

if (require.main === module) {
    main().catch(console.error);
}
