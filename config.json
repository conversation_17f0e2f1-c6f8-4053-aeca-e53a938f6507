{"searchSettings": {"keywords": ["program"], "searchTimeout": 300000, "enableProgressiveUpdates": true, "workerTimeout": 180000, "maxRetries": 2}, "concurrencySettings": {"maxConcurrentAccounts": 20, "workersPerAccount": 5, "delayBetweenAccounts": 1000, "delayBetweenBatches": 2000, "workerMemoryLimit": "1gb", "maxWorkersPerArchive": 10, "volumeSearchConcurrency": 8}, "outputSettings": {"resultFile": "search_results/RESULT.json", "debugLogFile": "DEBUG_LOGS.txt", "enableDetailedLogging": false, "preserveIndividualResults": false}, "authenticationSettings": {"loginTimeout": 60000, "retryAttempts": 3, "retryDelay": 5000, "connectionTimeout": 30000}, "archiveSettings": {"discoverAllArchives": true, "searchAllVolumes": true, "volumeTimeout": 180000, "maxVolumeDepth": 3}, "backupFilterSettings": {"enableRecentBackupsOnly": false, "maxRecentBackups": 10, "backupSelectionStrategy": "all", "skipOlderBackups": false, "logSkippedBackups": true}, "dashboardSettings": {"enableAutoRefresh": true, "refreshInterval": 5000, "showAccountBreakdown": true}, "errorHandling": {"enableGracefulShutdown": true, "workerRestartOnFailure": true, "maxWorkerRestarts": 2, "logWorkerExits": true}}