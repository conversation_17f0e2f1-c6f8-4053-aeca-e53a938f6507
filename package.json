{"name": "acronis-multi-account-search", "version": "2.0.0", "description": "Production backup search system for multiple Acronis accounts", "main": "multi_account_search.js", "scripts": {"start": "node multi_account_search.js"}, "dependencies": {"axios": "^1.7.7", "axios-cookiejar-support": "^5.0.3", "tough-cookie": "^5.0.0"}, "devDependencies": {}, "keywords": ["acronis", "backup", "search", "multi-account"], "author": "@nixnode", "license": "MIT", "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "https://github.com/nixnode/acronis-multi-search"}, "bugs": {"url": "https://github.com/nixnode/acronis-multi-search/issues"}, "homepage": "https://github.com/nixnode/acronis-multi-search#readme"}