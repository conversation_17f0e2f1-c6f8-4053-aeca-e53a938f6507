const axios = require('axios');
const { wrapper } = require('axios-cookiejar-support');
const { <PERSON>ieJar } = require('tough-cookie');
const fs = require('fs');
const path = require('path');

// Account status logging function
async function logAccountStatus(accountEmail, archiveId, archiveName, status, details = {}) {
  try {
    const statusFile = `search_results/status_${accountEmail.replace(/[@.]/g, '_')}.txt`;
    const timestamp = new Date().toISOString();

    // Ensure directory exists
    const dir = path.dirname(statusFile);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    let logEntry = `[${timestamp}] ${status}\n`;
    logEntry += `  Account: ${accountEmail}\n`;
    logEntry += `  Archive: ${archiveName} (${archiveId})\n`;

    if (details.backup) logEntry += `  Backup: ${details.backup}\n`;
    if (details.volume) logEntry += `  Volume: ${details.volume}\n`;
    if (details.volumePath) logEntry += `  Volume Path: ${details.volumePath}\n`;
    if (details.resultCount !== undefined) logEntry += `  Results Found: ${details.resultCount}\n`;
    if (details.error) logEntry += `  Error: ${details.error}\n`;
    if (details.statusCode) logEntry += `  Status Code: ${details.statusCode}\n`;
    if (details.duration) logEntry += `  Duration: ${details.duration}ms\n`;
    if (details.retryAttempt) logEntry += `  Retry Attempt: ${details.retryAttempt}\n`;
    if (details.totalVolumes) logEntry += `  Total Volumes: ${details.totalVolumes}\n`;
    if (details.completedVolumes !== undefined) logEntry += `  Completed Volumes: ${details.completedVolumes}\n`;
    if (details.failedVolumes !== undefined) logEntry += `  Failed Volumes: ${details.failedVolumes}\n`;

    logEntry += '\n';

    // Append to file
    fs.appendFileSync(statusFile, logEntry);
  } catch (error) {
    console.error(`❌ Failed to log account status: ${error.message}`);
  }
}

// Volume summary logging function
async function logVolumeSummary(accountEmail, archiveId, archiveName, backupName, volumeStats) {
  try {
    const statusFile = `search_results/status_${accountEmail.replace(/[@.]/g, '_')}.txt`;
    const timestamp = new Date().toISOString();

    let logEntry = `[${timestamp}] VOLUME_SUMMARY\n`;
    logEntry += `  Account: ${accountEmail}\n`;
    logEntry += `  Archive: ${archiveName}\n`;
    logEntry += `  Backup: ${backupName}\n`;
    logEntry += `  Total Volumes: ${volumeStats.total}\n`;
    logEntry += `  Successful: ${volumeStats.successful}\n`;
    logEntry += `  Failed: ${volumeStats.failed}\n`;
    logEntry += `  Total Results: ${volumeStats.totalResults}\n`;
    logEntry += `  Success Rate: ${((volumeStats.successful / volumeStats.total) * 100).toFixed(1)}%\n`;

    if (volumeStats.failedVolumes && volumeStats.failedVolumes.length > 0) {
      logEntry += `  Failed Volumes:\n`;
      volumeStats.failedVolumes.forEach(vol => {
        logEntry += `    - ${vol.name}: ${vol.error} (${vol.statusCode || 'N/A'})\n`;
      });
    }

    logEntry += '\n';

    // Append to file
    fs.appendFileSync(statusFile, logEntry);
  } catch (error) {
    console.error(`❌ Failed to log volume summary: ${error.message}`);
  }
}

// Progressive result saving function with dashboard-compatible format
async function saveProgressiveResults(accountEmail, results, archiveId, archiveName, backupName = 'Unknown Backup') {
  try {
    const resultFile = 'search_results/RESULT.json';

    // Ensure directory exists
    const dir = path.dirname(resultFile);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    let existingData = {
      timestamp: new Date().toISOString(),
      keywords: ['program'],
      searchDuration: 0,
      totalAccounts: 2,
      successfulAccounts: 0,
      failedAccounts: 0,
      totalResults: 0,
      progressiveUpdate: true,
      processedAccounts: 0,
      results: []
    };

    // Read existing results if file exists
    if (fs.existsSync(resultFile)) {
      try {
        const fileContent = fs.readFileSync(resultFile, 'utf8');
        existingData = JSON.parse(fileContent);
      } catch (error) {
        console.log(`⚠️  [${accountEmail}] Could not read existing results file: ${error.message}`);
      }
    }

    // Add new results in dashboard-compatible format
    if (results && results.length > 0) {
      // Check if existing data is in old flat format and convert it
      if (existingData.results && Array.isArray(existingData.results) && existingData.results.length > 0) {
        // Check if first item has backupName property (new format) or not (old format)
        if (!existingData.results[0].hasOwnProperty('backupName')) {
          console.log(`🔄 [${accountEmail}] Converting old format to dashboard-compatible format...`);

          // Convert old flat format to new dashboard format
          const groupedByBackup = {};

          // Group old results by backup folder
          existingData.results.forEach(item => {
            const key = `${item.backupFolder}-${item.accountEmail}-${item.archiveName}`;
            if (!groupedByBackup[key]) {
              groupedByBackup[key] = {
                backupName: item.backupFolder,
                accountEmail: item.accountEmail,
                archiveName: item.archiveName,
                searchKeyword: 'program',
                results: []
              };
            }
            groupedByBackup[key].results.push(item);
          });

          // Convert to array
          existingData.results = Object.values(groupedByBackup);
          console.log(`✅ [${accountEmail}] Converted ${existingData.results.length} backup groups`);
        }
      }

      // Find existing backup entry or create new one
      let backupEntry = existingData.results.find(r =>
        r.backupName === backupName &&
        r.accountEmail === accountEmail &&
        r.archiveName === archiveName
      );

      if (!backupEntry) {
        backupEntry = {
          backupName: backupName,
          accountEmail: accountEmail,
          archiveName: archiveName,
          searchKeyword: 'program',
          results: []
        };
        existingData.results.push(backupEntry);
      }

      // Add new results to this backup entry
      backupEntry.results.push(...results);

      // Update metadata
      existingData.totalResults = existingData.results.reduce((sum, backup) => sum + (backup.results ? backup.results.length : 0), 0);
      existingData.lastUpdated = new Date().toISOString();
      existingData.lastArchive = { archiveId, archiveName, accountEmail, backupName };

      // Write updated results back to file
      fs.writeFileSync(resultFile, JSON.stringify(existingData, null, 2));
      console.log(`💾 [${accountEmail}] Saved ${results.length} new results to ${backupName}. Total: ${existingData.totalResults}`);
    }

    return existingData.totalResults;
  } catch (error) {
    console.error(`❌ [${accountEmail}] Failed to save progressive results: ${error.message}`);
    return 0;
  }
}

function generateRequestId() {
  return 'f' + Math.random().toString(16).substring(2, 17);
}

async function findVolumesRecursively(client, archiveId, currentPath = "", maxDepth = 2, currentDepth = 0) {
  if (currentDepth >= maxDepth) {
    return [];
  }

  // SPECIAL CASE: If the backup path itself looks like a drive (C:, D:, etc.),
  // treat the backup as the drive volume and search it directly
  if (currentPath && currentPath.match(/^[A-Z]:$/)) {
    console.log(`🎯 DRIVE BACKUP DETECTED: "${currentPath}" is a drive backup - treating as drive volume`);
    return [{
      name: currentPath,
      id: currentPath,
      itemType: "DriveBackup",
      folderItem: true
    }];
  }

  try {
    const response = await client.get("https://cloud-wr-us2.acronis.com/ui/content", {
      headers: {
        "Accept": "application/json, text/plain, */*",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
        "Referer": "https://cloud-wr-us2.acronis.com/",
        "UI-REQUEST-ID": generateRequestId(),
      },
      params: {
        archiveId: archiveId,
        path: currentPath
      }
    });

    if (!response.data || !response.data.data) {
      return [];
    }

    const items = response.data.data;
    const volumes = [];

    // Debug: Log all items found in the backup folder
    console.log(`🐛 DEBUG: Found ${items.length} items in ${currentPath}:`);
    items.forEach((item, index) => {
      console.log(`   ${index + 1}. "${item.name}" (Type: ${item.itemType}, Folder: ${item.folderItem}, ID: ${item.id})`);
    });

    const driveItems = items.filter(item =>
      item.itemType === "FixedDrive" ||
      item.itemType === "RemovableDrive" ||
      (item.folderItem && item.name && item.name.match(/^[A-Z]:\s*\(.*\)$/))
    );

    console.log(`🐛 DEBUG: Filtered drive items: ${driveItems.length}`);
    driveItems.forEach((item, index) => {
      console.log(`   Drive ${index + 1}: "${item.name}" (Type: ${item.itemType})`);
    });

    if (driveItems.length > 0) {
      console.log(`🔍 Found ${driveItems.length} drive volumes at path "${currentPath}": ${driveItems.map(v => v.name).join(', ')}`);
    }
    volumes.push(...driveItems);

    // Also check for MountPoint items which might be the actual volumes we need
    const mountPointItems = items.filter(item =>
      item.itemType === "MountPoint" && item.folderItem
    );

    console.log(`🐛 DEBUG: Found MountPoint items: ${mountPointItems.length}`);
    mountPointItems.forEach((item, index) => {
      console.log(`   MountPoint ${index + 1}: "${item.name}" (ID: ${item.id})`);
    });

    if (mountPointItems.length > 0) {
      console.log(`🔍 Found ${mountPointItems.length} mount point volumes at path "${currentPath}": ${mountPointItems.map(v => v.name).join(', ')}`);
      volumes.push(...mountPointItems);
    }

    // If no volumes found yet, try looking for any folder items that might be DRIVE volumes only
    if (volumes.length === 0) {
      const folderItems = items.filter(item => item.folderItem);
      console.log(`🐛 DEBUG: No volumes found, checking ${folderItems.length} folder items for DRIVE volumes only`);
      folderItems.forEach((item, index) => {
        console.log(`   Folder ${index + 1}: "${item.name}" (Type: ${item.itemType}, ID: ${item.id})`);
      });

      // ONLY add folder items that are actual DRIVE volumes - be very strict
      const potentialVolumes = folderItems.filter(item =>
        item.name && (
          item.name.match(/^[A-Z]:\s*\(.*\)$/) ||  // Drive pattern like "C: (Windows)"
          item.name.match(/^[A-Z]:\s*$/) ||        // Simple drive pattern like "C:"
          item.name.match(/^[A-Z]+_[A-Z]+$/)       // System pattern like "SYSTEM_DRV"
          // REMOVED: Generic parentheses pattern to avoid subdirectories
        )
      );

      // Additional filter to exclude common subdirectories that are NOT drives
      const excludePatterns = [
        /Program Files/i,
        /ProgramData/i,
        /Windows/i,
        /Users/<USER>
        /Documents/i,
        /AppData/i,
        /System32/i,
        /temp/i,
        /\$WINDOWS/i
      ];

      const actualDriveVolumes = potentialVolumes.filter(item =>
        !excludePatterns.some(pattern => pattern.test(item.name))
      );

      if (actualDriveVolumes.length > 0) {
        console.log(`🔍 Found ${actualDriveVolumes.length} actual drive volumes: ${actualDriveVolumes.map(v => v.name).join(', ')}`);
        volumes.push(...actualDriveVolumes);
      } else if (potentialVolumes.length > 0) {
        console.log(`⚠️  Found ${potentialVolumes.length} potential volumes but they appear to be subdirectories, not drives: ${potentialVolumes.map(v => v.name).join(', ')}`);
        console.log(`🎯 Skipping subdirectories - only searching actual drive volumes`);
      }
    }

    // Only explore subdirectories if we haven't found volumes yet and we're at the root level
    if (volumes.length === 0 && currentDepth === 0) {
      const folders = items.filter(item => item.folderItem && !item.name.includes("."));
      console.log(`🔍 No volumes found at root, exploring ${folders.length} folders for volumes`);

      // Only explore a few key folders to find volumes
      for (const folder of folders.slice(0, 3)) {
        const subPath = currentPath ? `${currentPath}/${folder.name}` : folder.name;
        try {
          const subVolumes = await findVolumesRecursively(client, archiveId, subPath, maxDepth, currentDepth + 1);
          volumes.push(...subVolumes);
        } catch (error) {
          console.log(`⚠️  Error exploring path ${subPath}: ${error.message}`);
        }
      }
    }

    return volumes;
  } catch (error) {
    console.log(`⚠️  Error exploring path ${currentPath}: ${error.message}`);
    return [];
  }
}

async function searchWorker(backup, searchKeyword, authCookies, archiveId, accountEmail = "unknown") {
  try {
    console.log(`🐛 DEBUG: Starting worker for backup ${backup.name}`);

    const jar = new CookieJar();
    authCookies.forEach(cookie => {
      jar.setCookieSync(`${cookie.name}=${cookie.value}`, "https://cloud-wr-us2.acronis.com");
    });

    console.log(`🐛 DEBUG: Restoring ${authCookies.length} cookies for ${backup.name}`);

    const client = wrapper(axios.create({
      jar,
      withCredentials: true
      // No timeout - wait for actual API response (200, 500, etc.)
    }));

    console.log(`🐛 DEBUG: Client created for ${backup.name} with no timeout - waiting for API response`);

    console.log(`🔍 Exploring backup structure: ${backup.name}`);
    const volumes = await findVolumesRecursively(client, archiveId, backup.name);

    if (volumes.length === 0) {
      console.log(`⚠️  No volumes found in ${backup.name}, using API searcher fallback...`);

      // Log API searcher fallback start
      await logAccountStatus(accountEmail, archiveId, archiveId.split('/').pop(), 'API_SEARCHER_FALLBACK_START', {
        backup: backup.name,
        error: 'No volumes found, using API searcher'
      });

      try {
        console.log(`🔍 Using API searcher for backup: ${backup.name}`);
        console.log(`   📋 Archive ID: ${archiveId}`);
        console.log(`   🔎 Search Text: ${searchKeyword}`);

        const requestId = generateRequestId();
        console.log(`🐛 DEBUG: Making API search request with ID: ${requestId}`);

        // Use API searcher with minimal parameters
        const searchParams = {
          archiveId: archiveId,
          searchText: searchKeyword,
          limit: 500000,
          offset: 0
        };

        console.log(`🐛 DEBUG: API searcher params:`, JSON.stringify(searchParams, null, 2));

        const searchResponse = await client.get("https://cloud-wr-us2.acronis.com/ui/search", {
          headers: {
            "Accept": "application/json, text/plain, */*",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
            "Referer": "https://cloud-wr-us2.acronis.com/",
            "UI-REQUEST-ID": requestId,
          },
          params: searchParams
        });

        console.log(`🐛 DEBUG: API searcher response received, status: ${searchResponse.status}`);

        if (searchResponse.status === 200 && searchResponse.data && searchResponse.data.data) {
          const searchResults = searchResponse.data.data;
          console.log(`✅ API searcher found ${searchResults.length} results in ${backup.name}`);

          // Log successful API searcher fallback
          await logAccountStatus(accountEmail, archiveId, archiveId.split('/').pop(), 'API_SEARCHER_FALLBACK_SUCCESS', {
            backup: backup.name,
            resultCount: searchResults.length,
            statusCode: searchResponse.status
          });

          const processedResults = searchResults.map(item => ({
            name: item.name,
            displayName: item.displayName || item.name,
            itemType: item.itemType,
            id: item.id,
            backupFolder: backup.name,
            archiveName: archiveId.split('/').pop(),
            accountEmail: accountEmail
          }));

          // Save results immediately for progressive updates
          try {
            const totalSaved = await saveProgressiveResults(accountEmail, processedResults, archiveId, archiveId.split('/').pop(), backup.name);
            console.log(`💾 [${accountEmail}] Progressive save completed for API searcher ${backup.name}. Total results in file: ${totalSaved}`);
          } catch (saveError) {
            console.error(`❌ [${accountEmail}] Progressive save failed for API searcher ${backup.name}: ${saveError.message}`);
          }

          return {
            backupName: backup.name,
            results: processedResults,
            error: null,
            message: `API searcher found ${processedResults.length} results`
          };
        } else {
          console.log(`⚠️  API searcher returned no results for ${backup.name}`);

          // Log empty API searcher fallback
          await logAccountStatus(accountEmail, archiveId, archiveId.split('/').pop(), 'API_SEARCHER_FALLBACK_EMPTY', {
            backup: backup.name,
            resultCount: 0,
            statusCode: searchResponse.status
          });
        }
      } catch (apiSearchError) {
        console.log(`❌ API searcher failed for ${backup.name}: ${apiSearchError.message}`);

        // Log failed API searcher fallback
        await logAccountStatus(accountEmail, archiveId, archiveId.split('/').pop(), 'API_SEARCHER_FALLBACK_FAILED', {
          backup: backup.name,
          error: apiSearchError.message,
          statusCode: apiSearchError.response?.status
        });
      }
      
      return {
        backupName: backup.name,
        results: [],
        error: null,
        message: "No volumes found and API searcher failed"
      };
    }

    console.log(`📁 Found ${volumes.length} DRIVE volumes in ${backup.name}:`);
    volumes.forEach((v, index) => {
      console.log(`   ${index + 1}. ${v.name} (ID: ${v.id}) - Type: ${v.itemType}`);
    });

    if (volumes.length > 0) {
      console.log(`🎯 Will search ONLY at drive level - not in subdirectories like Program Files`);
    }

    let allResults = [];

    // Search each volume with delays and retry logic for socket hang ups
    for (let i = 0; i < volumes.length; i++) {
      const volume = volumes[i];

      // Add delay between requests to prevent server overload
      if (i > 0) {
        console.log(`⏳ Waiting 2 seconds before next volume search...`);
        await new Promise(resolve => setTimeout(resolve, 2000)); // 2 second delay
      }

      // Retry logic for socket hang ups - up to 2 retries
      let retryCount = 0;
      const maxRetries = 2;
      let volumeSuccess = false;

      while (retryCount <= maxRetries && !volumeSuccess) {
        try {
          if (retryCount > 0) {
            console.log(`🔄 Retry attempt ${retryCount}/${maxRetries} for volume: ${volume.name}`);
            // Add extra delay for retries
            await new Promise(resolve => setTimeout(resolve, 3000));
          }

          console.log(`🔍 Searching in volume: ${volume.name} (${i + 1}/${volumes.length})`);
          console.log(`   📋 Archive ID: ${archiveId}`);
          console.log(`   📁 Volume Path: ${volume.id}`);
          console.log(`   🔎 Search Text: ${searchKeyword}`);

          // Log volume search start
          await logAccountStatus(accountEmail, archiveId, archiveId.split('/').pop(), 'VOLUME_SEARCH_START', {
            backup: backup.name,
            volume: volume.name,
            volumePath: volume.id
          });

          const requestId = generateRequestId();
          console.log(`🐛 DEBUG: Making search request with ID: ${requestId}`);

          const searchParams = {
            archiveId: archiveId,
            volumePath: volume.id,
            path: volume.id,
            searchText: searchKeyword,
            limit: 500000,  // Try to get more results
            offset: 0
          };

          console.log(`🐛 DEBUG: Search params:`, JSON.stringify(searchParams, null, 2));

          const searchResponse = await client.get("https://cloud-wr-us2.acronis.com/ui/search", {
            headers: {
              "Accept": "application/json, text/plain, */*",
              "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
              "Referer": "https://cloud-wr-us2.acronis.com/",
              "UI-REQUEST-ID": requestId,
            },
            params: searchParams
          });

          console.log(`🐛 DEBUG: Search response received for ${volume.name}, status: ${searchResponse.status}`);

          if (searchResponse.status !== 200) {
            console.log(`⚠️  Search API returned status ${searchResponse.status} for volume ${volume.name}`);
            break; // Exit retry loop for non-200 status
          }

          if (!searchResponse.data || !searchResponse.data.data) {
            console.log(`⚠️  No search data returned for volume ${volume.name}`);
            break; // Exit retry loop for no data
          }

          const searchResults = searchResponse.data.data;
          console.log(`📊 Found ${searchResults.length} results in volume ${volume.name}`);

          // Log successful volume search
          await logAccountStatus(accountEmail, archiveId, archiveId.split('/').pop(), 'VOLUME_SEARCH_SUCCESS', {
            backup: backup.name,
            volume: volume.name,
            volumePath: volume.id,
            resultCount: searchResults.length,
            statusCode: searchResponse.status
          });

          if (searchResults.length > 0) {
            const processedResults = searchResults.map(item => ({
              name: item.name,
              displayName: item.displayName || item.name,
              itemType: item.itemType,
              id: item.id,
              backupFolder: backup.name,
              archiveName: archiveId.split('/').pop(),
              accountEmail: accountEmail
            }));

            allResults.push(...processedResults);
            console.log(`✅ Added ${processedResults.length} results from volume ${volume.name}`);

            // Save results immediately for progressive updates
            try {
              const totalSaved = await saveProgressiveResults(accountEmail, processedResults, archiveId, archiveId.split('/').pop(), backup.name);
              console.log(`💾 [${accountEmail}] Progressive save completed for ${volume.name}. Total results in file: ${totalSaved}`);
            } catch (saveError) {
              console.error(`❌ [${accountEmail}] Progressive save failed for ${volume.name}: ${saveError.message}`);
            }
          }

          volumeSuccess = true; // Mark as successful to exit retry loop

        } catch (volumeError) {
          retryCount++;

          // Log volume search error
          await logAccountStatus(accountEmail, archiveId, archiveId.split('/').pop(), 'VOLUME_SEARCH_ERROR', {
            backup: backup.name,
            volume: volume.name,
            volumePath: volume.id,
            error: volumeError.message,
            statusCode: volumeError.response?.status,
            retryAttempt: retryCount
          });

          // Check if it's a socket hang up error that we should retry
          const isSocketHangUp = volumeError.message.includes('socket hang up') ||
                                volumeError.message.includes('ECONNRESET') ||
                                volumeError.message.includes('ENOTFOUND') ||
                                volumeError.message.includes('ETIMEDOUT');

          if (isSocketHangUp && retryCount <= maxRetries) {
            console.log(`❌ Socket hang up error on volume ${volume.name} (attempt ${retryCount}/${maxRetries + 1}): ${volumeError.message}`);
            console.log(`🔄 Will retry in 3 seconds...`);
            continue; // Continue to next retry iteration
          } else if (retryCount > maxRetries) {
            console.log(`❌ Max retries (${maxRetries}) exceeded for volume ${volume.name}: ${volumeError.message}`);
            break; // Exit retry loop
          } else {
            console.log(`❌ Non-retryable error on volume ${volume.name}: ${volumeError.message}`);
            break; // Exit retry loop for non-socket errors
          }
        }
      }

      // If all retries failed, try fallback (existing fallback logic will be triggered)
      if (!volumeSuccess) {
        console.log(`❌ All retries failed for volume ${volume.name}, trying searcher API fallback...`);

        // Log fallback attempt
        await logAccountStatus(accountEmail, archiveId, archiveId.split('/').pop(), 'VOLUME_FALLBACK_ATTEMPT', {
          backup: backup.name,
          volume: volume.name,
          volumePath: volume.id,
          error: 'Trying searcher API fallback'
        });

        try {
          const requestId = generateRequestId();
          console.log(`🐛 DEBUG: Making searcher API fallback request with ID: ${requestId}`);

          // Use searcher API with minimal parameters
          const fallbackParams = {
            archiveId: archiveId,
            searchText: searchKeyword,
            limit: 500000,
            offset: 0
          };

          console.log(`🐛 DEBUG: Searcher API fallback params:`, JSON.stringify(fallbackParams, null, 2));

          const fallbackResponse = await client.get("https://cloud-wr-us2.acronis.com/ui/search", {
            headers: {
              "Accept": "application/json, text/plain, */*",
              "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
              "Referer": "https://cloud-wr-us2.acronis.com/",
              "UI-REQUEST-ID": requestId,
            },
            params: fallbackParams
          });

          console.log(`🐛 DEBUG: Searcher API fallback response received for ${volume.name}, status: ${fallbackResponse.status}`);

          if (fallbackResponse.status === 200 && fallbackResponse.data && fallbackResponse.data.data) {
            const fallbackResults = fallbackResponse.data.data;
            console.log(`✅ Searcher API fallback found ${fallbackResults.length} results for volume ${volume.name}`);

            // Filter results that might be from this volume (if possible)
            const volumeResults = fallbackResults.filter(item =>
              item.id && item.id.includes(volume.name.replace(/[()]/g, ''))
            );

            if (volumeResults.length > 0) {
              console.log(`✅ Found ${volumeResults.length} results specifically for volume ${volume.name}`);

              // Log successful fallback
              await logAccountStatus(accountEmail, archiveId, archiveId.split('/').pop(), 'VOLUME_FALLBACK_SUCCESS', {
                backup: backup.name,
                volume: volume.name,
                volumePath: volume.id,
                resultCount: volumeResults.length
              });

              const mappedResults = volumeResults.map(item => ({
                name: item.name,
                displayName: item.displayName || item.name,
                itemType: item.itemType,
                id: item.id,
                backupFolder: backup.name,
                archiveName: archiveId.split('/').pop(),
                accountEmail: accountEmail
              }));

              allResults.push(...mappedResults);

              // Save results immediately for progressive updates
              try {
                const totalSaved = await saveProgressiveResults(accountEmail, mappedResults, archiveId, archiveId.split('/').pop(), backup.name);
                console.log(`💾 [${accountEmail}] Progressive save completed for fallback ${volume.name}. Total results in file: ${totalSaved}`);
              } catch (saveError) {
                console.error(`❌ [${accountEmail}] Progressive save failed for fallback ${volume.name}: ${saveError.message}`);
              }
            } else {
              console.log(`⚠️  No specific results found for volume ${volume.name} in searcher API fallback`);

              // Log empty fallback
              await logAccountStatus(accountEmail, archiveId, archiveId.split('/').pop(), 'VOLUME_FALLBACK_EMPTY', {
                backup: backup.name,
                volume: volume.name,
                volumePath: volume.id,
                resultCount: 0
              });
            }
          } else {
            console.log(`⚠️  Searcher API fallback returned no results for volume ${volume.name}`);

            // Log empty fallback
            await logAccountStatus(accountEmail, archiveId, archiveId.split('/').pop(), 'VOLUME_FALLBACK_EMPTY', {
              backup: backup.name,
              volume: volume.name,
              volumePath: volume.id,
              resultCount: 0
            });
          }
        } catch (fallbackError) {
          console.log(`❌ Searcher API fallback also failed for volume ${volume.name}: ${fallbackError.message}`);

          // Log failed fallback
          await logAccountStatus(accountEmail, archiveId, archiveId.split('/').pop(), 'VOLUME_FALLBACK_FAILED', {
            backup: backup.name,
            volume: volume.name,
            volumePath: volume.id,
            error: fallbackError.message,
            statusCode: fallbackError.response?.status
          });
        }
      }
    }

    const message = allResults.length > 0
      ? `Found ${allResults.length} results in ${volumes.length} volumes`
      : `No results found in ${volumes.length} volumes`;

    // Log backup completion summary
    await logAccountStatus(accountEmail, archiveId, archiveId.split('/').pop(), 'BACKUP_SEARCH_COMPLETE', {
      backup: backup.name,
      totalVolumes: volumes.length,
      resultCount: allResults.length
    });

    return {
      backupName: backup.name,
      results: allResults,
      error: null,
      message: message
    };

  } catch (error) {
    console.log(`❌ Worker error for ${backup.name}: ${error.message}`);
    return {
      backupName: backup.name,
      results: [],
      error: error.message,
      message: `Error: ${error.message}`
    };
  }
}

module.exports = {
  searchWorker,
  findVolumesRecursively,
  generateRequestId
};
