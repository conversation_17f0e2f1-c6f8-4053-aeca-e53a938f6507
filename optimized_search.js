const { Worker, is<PERSON>ain<PERSON>hread, parentPort, workerData } = require('worker_threads');
const axios = require('axios');
const { wrapper } = require('axios-cookiejar-support');
const { <PERSON>ieJar } = require('tough-cookie');
const fs = require('fs');
const path = require('path');

// Account status logging function
async function logAccountStatus(accountEmail, archiveId, archiveName, status, details = {}) {
  try {
    const statusFile = `search_results/status_${accountEmail.replace(/[@.]/g, '_')}.txt`;
    const timestamp = new Date().toISOString();

    // Ensure directory exists
    const dir = path.dirname(statusFile);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    let logEntry = `[${timestamp}] ${status}\n`;
    logEntry += `  Account: ${accountEmail}\n`;
    logEntry += `  Archive: ${archiveName} (${archiveId})\n`;

    if (details.backup) logEntry += `  Backup: ${details.backup}\n`;
    if (details.volume) logEntry += `  Volume: ${details.volume}\n`;
    if (details.volumePath) logEntry += `  Volume Path: ${details.volumePath}\n`;
    if (details.resultCount !== undefined) logEntry += `  Results Found: ${details.resultCount}\n`;
    if (details.error) logEntry += `  Error: ${details.error}\n`;
    if (details.statusCode) logEntry += `  Status Code: ${details.statusCode}\n`;
    if (details.duration) logEntry += `  Duration: ${details.duration}ms\n`;
    if (details.retryAttempt) logEntry += `  Retry Attempt: ${details.retryAttempt}\n`;
    if (details.totalVolumes) logEntry += `  Total Volumes: ${details.totalVolumes}\n`;
    if (details.completedVolumes !== undefined) logEntry += `  Completed Volumes: ${details.completedVolumes}\n`;
    if (details.failedVolumes !== undefined) logEntry += `  Failed Volumes: ${details.failedVolumes}\n`;

    logEntry += '\n';

    // Append to file
    fs.appendFileSync(statusFile, logEntry);
  } catch (error) {
    console.error(`❌ Failed to log account status: ${error.message}`);
  }
}

// Volume summary logging function
async function logVolumeSummary(accountEmail, archiveId, archiveName, backupName, volumeStats) {
  try {
    const statusFile = `search_results/status_${accountEmail.replace(/[@.]/g, '_')}.txt`;
    const timestamp = new Date().toISOString();

    let logEntry = `[${timestamp}] VOLUME_SUMMARY\n`;
    logEntry += `  Account: ${accountEmail}\n`;
    logEntry += `  Archive: ${archiveName}\n`;
    logEntry += `  Backup: ${backupName}\n`;
    logEntry += `  Total Volumes: ${volumeStats.total}\n`;
    logEntry += `  Successful: ${volumeStats.successful}\n`;
    logEntry += `  Failed: ${volumeStats.failed}\n`;
    logEntry += `  Total Results: ${volumeStats.totalResults}\n`;
    logEntry += `  Success Rate: ${((volumeStats.successful / volumeStats.total) * 100).toFixed(1)}%\n`;

    if (volumeStats.failedVolumes && volumeStats.failedVolumes.length > 0) {
      logEntry += `  Failed Volumes:\n`;
      volumeStats.failedVolumes.forEach(vol => {
        logEntry += `    - ${vol.name}: ${vol.error} (${vol.statusCode || 'N/A'})\n`;
      });
    }

    logEntry += '\n';

    // Append to file
    fs.appendFileSync(statusFile, logEntry);
  } catch (error) {
    console.error(`❌ Failed to log volume summary: ${error.message}`);
  }
}

function generateRequestId() {
  return 'f' + Math.random().toString(16).substring(2, 17);
}

// Progressive result saving function with dashboard-compatible format
async function saveProgressiveResults(accountEmail, results, archiveId, archiveName, backupName = 'Unknown Backup') {
  try {
    const resultFile = 'search_results/RESULT.json';

    // Ensure directory exists
    const dir = path.dirname(resultFile);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    let existingData = {
      timestamp: new Date().toISOString(),
      keywords: ['program'],
      searchDuration: 0,
      totalAccounts: 2,
      successfulAccounts: 0,
      failedAccounts: 0,
      totalResults: 0,
      progressiveUpdate: true,
      processedAccounts: 0,
      results: []
    };

    // Read existing results if file exists
    if (fs.existsSync(resultFile)) {
      try {
        const fileContent = fs.readFileSync(resultFile, 'utf8');
        existingData = JSON.parse(fileContent);
      } catch (error) {
        console.log(`⚠️  [${accountEmail}] Could not read existing results file: ${error.message}`);
      }
    }

    // Add new results in dashboard-compatible format
    if (results && results.length > 0) {
      // Check if existing data is in old flat format and convert it
      if (existingData.results && Array.isArray(existingData.results) && existingData.results.length > 0) {
        // Check if first item has backupName property (new format) or not (old format)
        if (!existingData.results[0].hasOwnProperty('backupName')) {
          console.log(`🔄 [${accountEmail}] Converting old format to dashboard-compatible format...`);

          // Convert old flat format to new dashboard format
          const groupedByBackup = {};

          // Group old results by backup folder
          existingData.results.forEach(item => {
            const key = `${item.backupFolder}-${item.accountEmail}-${item.archiveName}`;
            if (!groupedByBackup[key]) {
              groupedByBackup[key] = {
                backupName: item.backupFolder,
                accountEmail: item.accountEmail,
                archiveName: item.archiveName,
                searchKeyword: 'program',
                results: []
              };
            }
            groupedByBackup[key].results.push(item);
          });

          // Convert to array
          existingData.results = Object.values(groupedByBackup);
          console.log(`✅ [${accountEmail}] Converted ${existingData.results.length} backup groups`);
        }
      }

      // Find existing backup entry or create new one
      let backupEntry = existingData.results.find(r =>
        r.backupName === backupName &&
        r.accountEmail === accountEmail &&
        r.archiveName === archiveName
      );

      if (!backupEntry) {
        backupEntry = {
          backupName: backupName,
          accountEmail: accountEmail,
          archiveName: archiveName,
          searchKeyword: 'program',
          results: []
        };
        existingData.results.push(backupEntry);
      }

      // Add new results to this backup entry
      backupEntry.results.push(...results);

      // Update metadata
      existingData.totalResults = existingData.results.reduce((sum, backup) => sum + (backup.results ? backup.results.length : 0), 0);
      existingData.lastUpdated = new Date().toISOString();
      existingData.lastArchive = { archiveId, archiveName, accountEmail, backupName };

      // Write updated results back to file
      fs.writeFileSync(resultFile, JSON.stringify(existingData, null, 2));
      console.log(`💾 [${accountEmail}] Saved ${results.length} new results to ${backupName}. Total: ${existingData.totalResults}`);
    }

    return existingData.totalResults;
  } catch (error) {
    console.error(`❌ [${accountEmail}] Failed to save progressive results: ${error.message}`);
    return 0;
  }
}

if (isMainThread) {
  // Main thread - export functions for use by multi_account_search.js
  
  async function searchArchiveWithSearcherAPIForVolume(client, archiveId, searchKeyword, accountEmail, backup, progressCallback = null) {
    console.log(`🔍 [${accountEmail}] Using Searcher API for backup ${backup.name} in archive ${archiveId} with keyword "${searchKeyword}"`);

    // Log searcher API start
    await logAccountStatus(accountEmail, archiveId, archiveId.split('/').pop(), 'SEARCHER_API_START', {
      backup: backup.name,
      volume: backup.name
    });

    // Retry logic for searcher API - up to 3 retries
    let retryCount = 0;
    const maxRetries = 3;

    while (retryCount <= maxRetries) {
      try {
        if (retryCount > 0) {
          console.log(`🔄 [${accountEmail}] Searcher API retry attempt ${retryCount}/${maxRetries} for ${backup.name}`);
          await new Promise(resolve => setTimeout(resolve, 2000 * retryCount)); // Increasing delay
        }

        const requestId = generateRequestId();
        console.log(`🐛 DEBUG: [${accountEmail}] Making searcher API request for ${backup.name} with ID: ${requestId}`);

        const searchParams = {
          archiveId: archiveId,
          searchText: searchKeyword,
          path: backup.id || backup.name, // Use backup path if available
          limit: 500000,
          offset: 0
        };

        console.log(`🐛 DEBUG: [${accountEmail}] Searcher API params for ${backup.name}:`, JSON.stringify(searchParams, null, 2));

        const response = await client.get("https://cloud-wr-us2.acronis.com/ui/search", {
          headers: {
            "Accept": "application/json, text/plain, */*",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
            "Referer": "https://cloud-wr-us2.acronis.com/",
            "UI-REQUEST-ID": requestId,
          },
          params: searchParams
        });

        console.log(`🐛 DEBUG: [${accountEmail}] Searcher API response status for ${backup.name}: ${response.status}`);

        if (response.status === 200 && response.data && response.data.data) {
          const searchResults = response.data.data;
          console.log(`✅ [${accountEmail}] Searcher API found ${searchResults.length} results for ${backup.name}`);

          // Log successful searcher API
          await logAccountStatus(accountEmail, archiveId, archiveId.split('/').pop(), 'SEARCHER_API_SUCCESS', {
            backup: backup.name,
            volume: backup.name,
            resultCount: searchResults.length,
            statusCode: response.status
          });

          if (searchResults.length > 0) {
            const processedResults = searchResults.map(item => ({
              name: item.name,
              displayName: item.displayName || item.name,
              itemType: item.itemType,
              id: item.id,
              path: item.path || item.id,
              size: item.size || 0,
              type: item.type || 'unknown',
              lastModified: item.lastModified || null,
              backupFolder: backup.name,
              archiveName: archiveId.split('/').pop(),
              accountEmail: accountEmail,
              searchMetadata: {
                email: accountEmail,
                archiveId: archiveId,
                archiveName: archiveId.split('/').pop(),
                datacenter: archiveId.split('//')[1]?.split('/')[0] || 'unknown',
                searchTimestamp: new Date().toISOString(),
                searchKeyword: searchKeyword,
                searchMethod: 'searcher-api-volume',
                backupPath: backup.id || backup.name
              }
            }));

            // Save results immediately and call progress callback for live updates
            try {
              const totalSaved = await saveProgressiveResults(accountEmail, processedResults, archiveId, archiveId.split('/').pop(), backup.name);
              console.log(`💾 [${accountEmail}] Progressive save completed. Total results in file: ${totalSaved}`);
            } catch (saveError) {
              console.error(`❌ [${accountEmail}] Progressive save failed for ${backup.name}: ${saveError.message}`);
            }

            if (progressCallback) {
              try {
                console.log(`📊 [${accountEmail}] Calling progress callback with ${processedResults.length} results for ${backup.name}`);
                await progressCallback(accountEmail, [{
                  backupName: `${backup.name}-SearcherAPI`,
                  results: processedResults,
                  error: null,
                  message: `Searcher API found ${processedResults.length} results in ${backup.name}`,
                  archiveId: archiveId,
                  archiveName: archiveId.split('/').pop(),
                  datacenter: archiveId.split('//')[1]?.split('/')[0] || 'unknown'
                }]);
              } catch (callbackError) {
                console.error(`⚠️  [${accountEmail}] Progress callback failed for ${backup.name}: ${callbackError.message}`);
              }
            }

            return [{
              backupName: `${backup.name}-SearcherAPI`,
              results: processedResults,
              error: null,
              message: `Searcher API found ${processedResults.length} results in ${backup.name}`,
              archiveId: archiveId,
              archiveName: archiveId.split('/').pop(),
              datacenter: archiveId.split('//')[1]?.split('/')[0] || 'unknown'
            }];
          } else {
            console.log(`⚠️  [${accountEmail}] Searcher API returned no results for ${backup.name}`);

            // Log empty searcher API result
            await logAccountStatus(accountEmail, archiveId, archiveId.split('/').pop(), 'SEARCHER_API_EMPTY', {
              backup: backup.name,
              volume: backup.name,
              resultCount: 0,
              statusCode: response.status
            });

            return [];
          }
        } else {
          throw new Error(`Searcher API returned status ${response.status} for ${backup.name}`);
        }

      } catch (error) {
        retryCount++;

        // Log searcher API error
        await logAccountStatus(accountEmail, archiveId, archiveId.split('/').pop(), 'SEARCHER_API_ERROR', {
          backup: backup.name,
          volume: backup.name,
          error: error.message,
          statusCode: error.response?.status,
          retryAttempt: retryCount
        });

        const isRetryableError = error.message.includes('timeout') ||
                                error.message.includes('socket hang up') ||
                                error.message.includes('ECONNRESET') ||
                                error.message.includes('ENOTFOUND') ||
                                error.message.includes('ETIMEDOUT') ||
                                (error.response && error.response.status >= 500);

        if (isRetryableError && retryCount <= maxRetries) {
          console.log(`❌ [${accountEmail}] Searcher API retryable error for ${backup.name} (attempt ${retryCount}/${maxRetries + 1}): ${error.message}`);
          continue;
        } else if (retryCount > maxRetries) {
          console.log(`❌ [${accountEmail}] Searcher API max retries (${maxRetries}) exceeded for ${backup.name}: ${error.message}`);
        } else {
          console.log(`❌ [${accountEmail}] Searcher API non-retryable error for ${backup.name}: ${error.message}`);
        }

        // If searcher API fails completely, return empty results
        console.log(`⚠️  [${accountEmail}] Searcher API failed completely for ${backup.name}`);

        // Log final failure
        await logAccountStatus(accountEmail, archiveId, archiveId.split('/').pop(), 'SEARCHER_API_FAILED', {
          backup: backup.name,
          volume: backup.name,
          error: error.message,
          statusCode: error.response?.status
        });

        return [];
      }
    }

    return [];
  }

  async function searchArchiveWithSearcherAPI(client, archiveId, searchKeyword, accountEmail, progressCallback = null) {
    console.log(`🔍 [${accountEmail}] Using Searcher API for archive ${archiveId} with keyword "${searchKeyword}"`);
    
    // Retry logic for searcher API - up to 3 retries
    let retryCount = 0;
    const maxRetries = 3;
    
    while (retryCount <= maxRetries) {
      try {
        if (retryCount > 0) {
          console.log(`🔄 [${accountEmail}] Searcher API retry attempt ${retryCount}/${maxRetries}`);
          await new Promise(resolve => setTimeout(resolve, 2000 * retryCount)); // Increasing delay
        }

        const requestId = generateRequestId();
        console.log(`🐛 DEBUG: [${accountEmail}] Making searcher API request with ID: ${requestId}`);

        const searchParams = {
          archiveId: archiveId,
          searchText: searchKeyword,
          limit: 500000,
          offset: 0
        };

        console.log(`🐛 DEBUG: [${accountEmail}] Searcher API params:`, JSON.stringify(searchParams, null, 2));

        const response = await client.get("https://cloud-wr-us2.acronis.com/ui/search", {
          headers: {
            "Accept": "application/json, text/plain, */*",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
            "Referer": "https://cloud-wr-us2.acronis.com/",
            "UI-REQUEST-ID": requestId,
          },
          params: searchParams
        });

        console.log(`🐛 DEBUG: [${accountEmail}] Searcher API response status: ${response.status}`);

        if (response.status === 200 && response.data && response.data.data) {
          const searchResults = response.data.data;
          console.log(`✅ [${accountEmail}] Searcher API found ${searchResults.length} results`);

          if (searchResults.length > 0) {
            const processedResults = searchResults.map(item => ({
              name: item.name,
              displayName: item.displayName || item.name,
              itemType: item.itemType,
              id: item.id,
              path: item.path || item.id,
              size: item.size || 0,
              type: item.type || 'unknown',
              lastModified: item.lastModified || null,
              backupFolder: 'Searcher-API',
              archiveName: archiveId.split('/').pop(),
              accountEmail: accountEmail,
              searchMetadata: {
                email: accountEmail,
                archiveId: archiveId,
                archiveName: archiveId.split('/').pop(),
                datacenter: archiveId.split('//')[1]?.split('/')[0] || 'unknown',
                searchTimestamp: new Date().toISOString(),
                searchKeyword: searchKeyword,
                searchMethod: 'searcher-api'
              }
            }));

            // Save results immediately and call progress callback for live updates
            try {
              const totalSaved = await saveProgressiveResults(accountEmail, processedResults, archiveId, archiveId.split('/').pop(), 'Searcher-API');
              console.log(`💾 [${accountEmail}] Progressive save completed. Total results in file: ${totalSaved}`);
            } catch (saveError) {
              console.error(`❌ [${accountEmail}] Progressive save failed: ${saveError.message}`);
            }

            if (progressCallback) {
              try {
                console.log(`📊 [${accountEmail}] Calling progress callback with ${processedResults.length} results`);
                await progressCallback(accountEmail, [{
                  backupName: 'Searcher-API',
                  results: processedResults,
                  error: null,
                  message: `Searcher API found ${processedResults.length} results`,
                  archiveId: archiveId,
                  archiveName: archiveId.split('/').pop(),
                  datacenter: archiveId.split('//')[1]?.split('/')[0] || 'unknown'
                }]);
              } catch (callbackError) {
                console.error(`⚠️  [${accountEmail}] Progress callback failed: ${callbackError.message}`);
              }
            }

            return [{
              backupName: 'Searcher-API',
              results: processedResults,
              error: null,
              message: `Searcher API found ${processedResults.length} results`,
              archiveId: archiveId,
              archiveName: archiveId.split('/').pop(),
              datacenter: archiveId.split('//')[1]?.split('/')[0] || 'unknown'
            }];
          } else {
            console.log(`⚠️  [${accountEmail}] Searcher API returned no results`);
            return [];
          }
        } else {
          throw new Error(`Searcher API returned status ${response.status}`);
        }

      } catch (error) {
        retryCount++;
        
        const isRetryableError = error.message.includes('timeout') || 
                                error.message.includes('socket hang up') ||
                                error.message.includes('ECONNRESET') ||
                                error.message.includes('ENOTFOUND') ||
                                error.message.includes('ETIMEDOUT') ||
                                (error.response && error.response.status >= 500);

        if (isRetryableError && retryCount <= maxRetries) {
          console.log(`❌ [${accountEmail}] Searcher API retryable error (attempt ${retryCount}/${maxRetries + 1}): ${error.message}`);
          continue;
        } else if (retryCount > maxRetries) {
          console.log(`❌ [${accountEmail}] Searcher API max retries (${maxRetries}) exceeded: ${error.message}`);
        } else {
          console.log(`❌ [${accountEmail}] Searcher API non-retryable error: ${error.message}`);
        }
        
        // If searcher API fails completely, return empty results
        console.log(`⚠️  [${accountEmail}] Searcher API failed completely for archive ${archiveId}`);
        return [];
      }
    }

    return [];
  }

  async function searchInArchiveOptimized(client, cookies, archiveId, searchKeyword, accountEmail, progressCallback = null, config = null) {
    try {
      console.log(`🔍 [${accountEmail}] Starting volume-first optimized search in archive ${archiveId} for "${searchKeyword}"`);

      // Step 1: Volume-based search as primary method
      console.log(`🎯 [${accountEmail}] Step 1: Volume-based search (primary method)`);

      // Import the original search function
      const { searchInArchive: originalSearchInArchive } = require('./fast_search.js');
      const volumeResults = await originalSearchInArchive(client, cookies, archiveId, searchKeyword, accountEmail, progressCallback, config);

      // Step 2: After volume search, run searcher API for each discovered volume
      console.log(`🎯 [${accountEmail}] Step 2: Running searcher API for each volume location`);

      let allResults = volumeResults || [];
      let totalVolumeResults = allResults.reduce((sum, backup) => sum + backup.results.length, 0);
      console.log(`✅ [${accountEmail}] Volume-based search found: ${totalVolumeResults} results`);

      // Get backup folders to run searcher API per volume
      // Extract backup folders from volume results
      const backupFolders = [];
      if (volumeResults && volumeResults.length > 0) {
        volumeResults.forEach(result => {
          if (result.backupName && !backupFolders.find(b => b.name === result.backupName)) {
            backupFolders.push({ name: result.backupName, id: result.backupName });
          }
        });
      }

      if (backupFolders && backupFolders.length > 0) {
        console.log(`🔍 [${accountEmail}] Running searcher API for ${backupFolders.length} backup folders`);

        for (const backup of backupFolders) {
          try {
            console.log(`🎯 [${accountEmail}] Searcher API for backup: ${backup.name}`);

            // Try searcher API with backup-specific path
            const searcherResults = await searchArchiveWithSearcherAPIForVolume(
              client, archiveId, searchKeyword, accountEmail, backup, progressCallback
            );

            if (searcherResults && searcherResults.length > 0) {
              allResults.push(...searcherResults);
              const newResults = searcherResults.reduce((sum, result) => sum + result.results.length, 0);
              console.log(`✅ [${accountEmail}] Searcher API added ${newResults} results for ${backup.name}`);
            }

          } catch (error) {
            console.log(`⚠️  [${accountEmail}] Searcher API failed for backup ${backup.name}: ${error.message}`);
          }
        }
      }

      const finalTotal = allResults.reduce((sum, backup) => sum + backup.results.length, 0);
      console.log(`✅ [${accountEmail}] Combined search completed: ${finalTotal} total results`);
      return allResults;

    } catch (error) {
      console.error(`❌ [${accountEmail}] Optimized archive search failed:`, error.message);
      return [];
    }
  }

  module.exports = {
    searchInArchiveOptimized,
    searchArchiveWithSearcherAPI,
    searchArchiveWithSearcherAPIForVolume,
    saveProgressiveResults,
    generateRequestId
  };

} else {
  // Worker thread - this shouldn't be used in the optimized version
  // but keeping for compatibility
  console.log('Worker thread started for optimized search');
  parentPort.postMessage({ success: false, error: 'Optimized search does not use worker threads for searcher API' });
}
